services:
  import_reglementation.csv_importer:
    class: <PERSON><PERSON><PERSON>\import_reglementation\Service\CsvImporter
    arguments: ['@logger.factory', '@entity_type.manager', '@messenger', '@file_system']

  import_reglementation.csv_importer_rh:
    class: Drupal\import_reglementation\Service\CsvImporterRH
    arguments: ['@entity_type.manager', '@logger.factory']

  import_reglementation.pdf_organizer:
    class: Drupal\import_reglementation\Service\PdfOrganizer
    arguments: ['@file_system', '@logger.factory', '@entity_type.manager']

  import_reglementation.pdf_title_attachment_commands:
    class: Drush\Commands\import_reglementation\PdfTitleAttachmentCommands
    arguments: ['@entity_type.manager', '@file_system']
    tags:
      - { name: drush.command }